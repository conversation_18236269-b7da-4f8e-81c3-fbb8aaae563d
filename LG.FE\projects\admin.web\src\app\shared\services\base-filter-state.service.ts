import { Injectable, signal, computed, inject, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { IBasedDataGridRequest, GeneralService } from 'SharedModules.Library';

/**
 * Generic filter change event interface
 */
export interface IFilterChangeEvent<TRequest> {
  filterName: keyof TRequest;
  value: any;
  resetPage?: boolean;
}

/**
 * Generic filter action event interface
 */
export interface IFilterActionEvent<TRequest> {
  action: 'search' | 'reset' | 'clear';
  filters: TRequest;
}

/**
 * Generic filter state interface
 */
export interface IBaseFilterState<TRequest> {
  queryParams: TRequest;
  isFilterOpen: boolean;
  hasUnsavedChanges?: boolean;
}

/**
 * Generic filter configuration interface
 */
export interface IBaseFilterConfig {
  showToggleButton?: boolean;
  defaultOpen?: boolean;
  enableAutoSearch?: boolean;
  searchDebounceMs?: number;
  validateOnChange?: boolean;
  resetToDefaults?: boolean;
}

/**
 * Filter validation result interface
 */
export interface IFilterValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * Generic base service for filter state management
 * Provides common patterns for filter state management, validation, and event handling
 */
@Injectable()
export abstract class BaseFilterStateService<
  TRequest extends IBasedDataGridRequest = IBasedDataGridRequest
> {
  protected destroyRef = inject(DestroyRef);
  protected generalService = inject(GeneralService);

  // === REACTIVE STATE SIGNALS ===
  protected _tempFilters = signal<Partial<TRequest>>({});
  protected _isFilterOpen = signal<boolean>(true);
  protected _filterState = signal<IBaseFilterState<TRequest>>({
    queryParams: {} as TRequest,
    isFilterOpen: true,
    hasUnsavedChanges: false
  });
  protected _validationErrors = signal<string[]>([]);

  // === PUBLIC COMPUTED PROPERTIES ===
  readonly isFilterOpen = computed(() => this._isFilterOpen());
  readonly tempFilters = computed(() => this._tempFilters());
  readonly filterState = computed(() => this._filterState());
  readonly validationErrors = computed(() => this._validationErrors());
  
  // Computed properties that merge temp + current state
  readonly currentFilters = computed(() => ({
    ...this._filterState().queryParams,
    ...this._tempFilters()
  }));

  readonly hasUnsavedChanges = computed(() => {
    return Object.keys(this._tempFilters()).length > 0;
  });

  readonly isValid = computed(() => {
    return this._validationErrors().length === 0;
  });

  // === ABSTRACT METHODS ===
  abstract getConfig(): IBaseFilterConfig;
  abstract getDefaultRequest(): TRequest;
  abstract validateFilters(filters: TRequest): IFilterValidationResult;

  /**
   * Initialize filter state
   */
  initialize(initialState: IBaseFilterState<TRequest>): void {
    this._filterState.set(initialState);
    this._isFilterOpen.set(initialState.isFilterOpen);
    this._tempFilters.set({});
    this.validateCurrentFilters();
  }

  /**
   * Update filter state from parent component
   */
  updateFilterState(newState: IBaseFilterState<TRequest>): void {
    this._filterState.set(newState);
    this._tempFilters.set({}); // Clear temp changes when state updates
    this.validateCurrentFilters();
  }

  /**
   * Handle filter value changes (temporary state)
   */
  onFilterChange(event: IFilterChangeEvent<TRequest>): void {
    this._tempFilters.update(current => ({
      ...current,
      [event.filterName]: event.value
    }));

    const config = this.getConfig();
    if (config.validateOnChange) {
      this.validateCurrentFilters();
    }
  }

  /**
   * Apply temporary filters (commit changes)
   */
  applyFilters(): IFilterActionEvent<TRequest> {
    const mergedFilters = this.currentFilters();
    const validation = this.validateFilters(mergedFilters as TRequest);
    
    if (!validation.isValid) {
      this._validationErrors.set(validation.errors);
      console.warn('Filter validation failed:', validation.errors);
      return {
        action: 'search',
        filters: this._filterState().queryParams
      };
    }

    // Clear temporary filters and validation errors after applying
    this._tempFilters.set({});
    this._validationErrors.set([]);
    
    return {
      action: 'search',
      filters: mergedFilters as TRequest
    };
  }

  /**
   * Reset all filters to default values
   */
  resetFilters(): IFilterActionEvent<TRequest> {
    const defaultRequest = this.getDefaultRequest();
    this._tempFilters.set({});
    this._validationErrors.set([]);
    
    return {
      action: 'reset',
      filters: defaultRequest
    };
  }

  /**
   * Clear temporary filter changes without applying
   */
  clearTempFilters(): IFilterActionEvent<TRequest> {
    this._tempFilters.set({});
    this._validationErrors.set([]);
    
    return {
      action: 'clear',
      filters: this._filterState().queryParams
    };
  }

  /**
   * Toggle filter panel visibility
   */
  toggleFilterPanel(): void {
    this._isFilterOpen.update(current => !current);
  }

  /**
   * Set filter panel visibility
   */
  setFilterPanelVisibility(isOpen: boolean): void {
    this._isFilterOpen.set(isOpen);
  }

  /**
   * Get current filter value (including temporary changes)
   */
  getFilterValue<K extends keyof TRequest>(filterName: K): TRequest[K] {
    const tempValue = this._tempFilters()[filterName];
    if (tempValue !== undefined) {
      return tempValue;
    }
    return this._filterState().queryParams[filterName];
  }

  /**
   * Set a specific filter value
   */
  setFilterValue<K extends keyof TRequest>(filterName: K, value: TRequest[K]): void {
    this._tempFilters.update(current => ({
      ...current,
      [filterName]: value
    }));

    const config = this.getConfig();
    if (config.validateOnChange) {
      this.validateCurrentFilters();
    }
  }

  /**
   * Check if a specific filter has a non-default value
   */
  isFilterActive<K extends keyof TRequest>(filterName: K): boolean {
    const currentValue = this.getFilterValue(filterName);
    const defaultValue = this.getDefaultRequest()[filterName];
    
    return !this.areValuesEqual(currentValue, defaultValue);
  }

  /**
   * Get count of active filters (non-default values)
   */
  getActiveFiltersCount(): number {
    const defaultRequest = this.getDefaultRequest();
    const currentFilters = this.currentFilters();
    let count = 0;

    for (const [key, value] of Object.entries(currentFilters)) {
      const defaultValue = (defaultRequest as any)[key];
      
      if (!this.areValuesEqual(value, defaultValue)) {
        count++;
      }
    }

    return count;
  }

  /**
   * Reset specific filter to default value
   */
  resetFilter<K extends keyof TRequest>(filterName: K): void {
    const defaultRequest = this.getDefaultRequest();
    const defaultValue = defaultRequest[filterName];
    this.setFilterValue(filterName, defaultValue);
  }

  /**
   * Get all active filters (non-default values)
   */
  getActiveFilters(): Partial<TRequest> {
    const defaultRequest = this.getDefaultRequest();
    const currentFilters = this.currentFilters();
    const activeFilters: Partial<TRequest> = {};

    for (const [key, value] of Object.entries(currentFilters)) {
      const defaultValue = (defaultRequest as any)[key];
      
      if (!this.areValuesEqual(value, defaultValue)) {
        (activeFilters as any)[key] = value;
      }
    }

    return activeFilters;
  }

  /**
   * Check if current filters are different from saved state
   */
  hasChanges(): boolean {
    return this.hasUnsavedChanges();
  }

  /**
   * Get filter summary for display
   */
  getFilterSummary(): string {
    const activeFilters = this.getActiveFilters();
    const filterCount = Object.keys(activeFilters).length;
    
    if (filterCount === 0) {
      return 'No filters applied';
    }
    
    return `${filterCount} filter${filterCount > 1 ? 's' : ''} applied`;
  }

  // === PROTECTED HELPER METHODS ===

  /**
   * Validate current filters and update validation state
   */
  protected validateCurrentFilters(): void {
    const mergedFilters = this.currentFilters();
    const validation = this.validateFilters(mergedFilters as TRequest);
    this._validationErrors.set(validation.errors);
  }

  /**
   * Default filter validation (can be overridden)
   */
  protected defaultValidateFilters(filters: TRequest): IFilterValidationResult {
    const errors: string[] = [];

    // Basic validation for common fields
    if (filters.pageNumber && filters.pageNumber < 1) {
      errors.push('Page number must be greater than 0');
    }

    if (filters.pageSize && filters.pageSize < 1) {
      errors.push('Page size must be greater than 0');
    }

    if (filters.pageSize && filters.pageSize > 100) {
      errors.push('Page size cannot exceed 100');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Helper method to safely convert values to appropriate types
   */
  protected convertFilterValue(value: any, targetType: 'string' | 'number' | 'boolean' | 'date'): any {
    if (value === null || value === undefined || value === '') {
      return null;
    }

    switch (targetType) {
      case 'string':
        return String(value);
      case 'number':
        const num = Number(value);
        return isNaN(num) ? null : num;
      case 'boolean':
        return Boolean(value);
      case 'date':
        const date = new Date(value);
        return isNaN(date.getTime()) ? null : date;
      default:
        return value;
    }
  }

  /**
   * Helper method to check if two values are equal
   */
  protected areValuesEqual(value1: any, value2: any): boolean {
    // Handle null/undefined
    if (value1 === null || value1 === undefined) {
      return value2 === null || value2 === undefined;
    }
    
    if (value2 === null || value2 === undefined) {
      return false;
    }

    // Handle arrays
    if (Array.isArray(value1) && Array.isArray(value2)) {
      return value1.length === value2.length && 
             value1.every((item, index) => this.areValuesEqual(item, value2[index]));
    }

    // Handle dates
    if (value1 instanceof Date && value2 instanceof Date) {
      return value1.getTime() === value2.getTime();
    }

    // Handle objects
    if (typeof value1 === 'object' && typeof value2 === 'object') {
      return JSON.stringify(value1) === JSON.stringify(value2);
    }

    // Handle primitives
    return value1 === value2;
  }

  /**
   * Helper method to check if a filter value is empty/null
   */
  protected isFilterEmpty(value: any): boolean {
    return value === null || 
           value === undefined || 
           value === '' || 
           (Array.isArray(value) && value.length === 0);
  }
}
